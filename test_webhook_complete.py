#!/usr/bin/env python3
"""
Script completo para probar que los webhooks se ejecutan correctamente.
"""

import requests
import time
import json
import threading
from datetime import datetime
from fastapi import FastAPI, Request
import uvicorn

# Configuración
LUMUSAI_URL = "http://localhost:8000"
WEBHOOK_PORT = 8001
WEBHOOK_URL = f"http://localhost:{WEBHOOK_PORT}/webhook"

# Variable global para almacenar webhooks recibidos
received_webhooks = []
webhook_received_event = threading.Event()

# Crear servidor de webhook
app = FastAPI()

@app.post("/webhook")
async def receive_webhook(request: Request):
    """Recibe el webhook de LumusAI."""
    global received_webhooks, webhook_received_event
    
    payload = await request.json()
    payload["received_at"] = datetime.utcnow().isoformat()
    received_webhooks.append(payload)
    
    print(f"\n🎉 ¡WEBHOOK RECIBIDO!")
    print(f"📋 Task ID: {payload.get('task_id')}")
    print(f"📊 Status: {payload.get('status')}")
    print(f"⏱️  Processing Time: {payload.get('processing_time')} seconds")
    
    # Señalar que se recibió el webhook
    webhook_received_event.set()
    
    return {"status": "received", "task_id": payload.get('task_id')}

def start_webhook_server():
    """Inicia el servidor de webhook en un hilo separado."""
    uvicorn.run(app, host="127.0.0.1", port=WEBHOOK_PORT, log_level="error")

def test_webhook_integration():
    """Prueba completa de integración de webhooks."""
    
    print("🧪 PRUEBA COMPLETA DE WEBHOOKS")
    print("="*50)
    
    # 1. Verificar que LumusAI esté corriendo
    print("1️⃣ Verificando que LumusAI esté corriendo...")
    try:
        response = requests.get(f"{LUMUSAI_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ LumusAI está corriendo")
        else:
            print("❌ LumusAI no responde correctamente")
            return False
    except Exception as e:
        print(f"❌ No se puede conectar a LumusAI: {e}")
        print("💡 Asegúrate de que el contenedor esté corriendo:")
        print("   docker ps")
        return False
    
    # 2. Iniciar servidor de webhook
    print("2️⃣ Iniciando servidor de webhook...")
    webhook_thread = threading.Thread(target=start_webhook_server, daemon=True)
    webhook_thread.start()
    time.sleep(2)  # Dar tiempo para que inicie
    print(f"✅ Servidor de webhook corriendo en {WEBHOOK_URL}")
    
    # 3. Enviar tarea de procesamiento
    print("3️⃣ Enviando tarea de procesamiento en background...")
    
    test_data = """
    Juan Pérez
    Ingeniero de Software Senior
    Email: <EMAIL>
    
    EXPERIENCIA:
    - Desarrollador Python (5 años)
    - Especialista en FastAPI y Docker
    - Líder técnico en proyectos de IA
    
    HABILIDADES:
    - Python, FastAPI, Docker
    - Machine Learning, NLP
    - Arquitectura de microservicios
    """
    
    try:
        start_time = time.time()
        
        response = requests.post(
            f"{LUMUSAI_URL}/process-test",
            data={
                "action": "cv",
                "data": test_data,
                "webhook_url": WEBHOOK_URL
            },
            timeout=30
        )
        
        submission_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("task_id")
            
            print(f"✅ Tarea enviada exitosamente en {submission_time:.2f} segundos")
            print(f"📋 Task ID: {task_id}")
            print(f"🔄 Status: {result.get('status')}")
            
        else:
            print(f"❌ Error enviando tarea: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error enviando tarea: {e}")
        return False
    
    # 4. Esperar el webhook
    print("4️⃣ Esperando webhook de completación...")
    print("⏳ Esto puede tomar 2-5 minutos...")
    
    # Esperar hasta 10 minutos por el webhook
    webhook_timeout = 600  # 10 minutos
    
    if webhook_received_event.wait(timeout=webhook_timeout):
        print("🎉 ¡WEBHOOK RECIBIDO EXITOSAMENTE!")
        
        # Mostrar detalles del webhook
        latest_webhook = received_webhooks[-1]
        print("\n📋 DETALLES DEL WEBHOOK:")
        print(f"   Task ID: {latest_webhook.get('task_id')}")
        print(f"   Status: {latest_webhook.get('status')}")
        print(f"   Processing Time: {latest_webhook.get('processing_time')} seconds")
        print(f"   Received At: {latest_webhook.get('received_at')}")
        
        if latest_webhook.get('status') == 'completed':
            print("✅ PROCESAMIENTO COMPLETADO EXITOSAMENTE")
            data = latest_webhook.get('data', {})
            if isinstance(data, dict) and data:
                print(f"📄 Datos extraídos: {list(data.keys())}")
            return True
        else:
            print("❌ PROCESAMIENTO FALLÓ")
            error = latest_webhook.get('data', {}).get('error', 'Unknown error')
            print(f"🚨 Error: {error}")
            return False
    else:
        print("⏰ TIMEOUT: No se recibió webhook en 10 minutos")
        print("💡 Posibles causas:")
        print("   - El procesamiento está tomando más tiempo del esperado")
        print("   - Hay un error en el sistema de webhooks")
        print("   - Problema de conectividad")
        return False

def main():
    """Función principal."""
    print("🚀 INICIANDO PRUEBA DE WEBHOOKS DE LUMUSAI")
    print("="*60)
    
    success = test_webhook_integration()
    
    print("\n" + "="*60)
    if success:
        print("🎉 ¡PRUEBA EXITOSA! Los webhooks funcionan correctamente.")
        print("✅ El sistema notifica correctamente cuando las tareas se completan.")
    else:
        print("❌ PRUEBA FALLIDA. Los webhooks no están funcionando correctamente.")
        print("🔧 Revisa los logs del contenedor Docker:")
        print("   docker logs lumusai-app")
    
    print("="*60)

if __name__ == "__main__":
    main()
