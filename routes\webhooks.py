import json
from datetime import datetime
from fastapi import APIRouter, HTTPException, Request, Body
from typing import Dict, Any

from models.job_status import WebhookPayload

router = APIRouter()


@router.post("/webhook/test", summary="Test webhook endpoint")
async def test_webhook_endpoint(
    request: Request,
    payload: WebhookPayload
):
    """
    Test webhook endpoint to verify webhook functionality.
    
    This endpoint can be used to test webhook notifications during development.
    It simply logs the received payload and returns a confirmation.
    
    Args:
        payload: Webhook payload containing job completion data
        
    Returns:
        dict: Confirmation of webhook receipt
        
    Example webhook payload:
    ```json
    {
        "task_id": "cv-1234567890-abc123",
        "status": "completed",
        "timestamp": "2024-01-15T10:30:00Z",
        "data": {
            "personal_info": {
                "full_name": "<PERSON> Doe",
                "email": "<EMAIL>"
            }
        },
        "processing_time": 245.6
    }
    ```
    """
    logger = request.app.state.logger
    
    logger.info(f"Webhook received for task {payload.task_id} with status {payload.status}")
    logger.info(f"Webhook payload: {payload.json()}")
    
    return {
        "status": "received",
        "message": f"Webhook for task {payload.task_id} processed successfully",
        "received_at": datetime.utcnow().isoformat(),
        "task_id": payload.task_id,
        "task_status": payload.status
    }


@router.post("/webhook/custom", summary="Custom webhook endpoint")
async def custom_webhook_endpoint(
    request: Request,
    payload: Dict[str, Any] = Body(...)
):
    """
    Custom webhook endpoint for handling any webhook format.
    
    This endpoint accepts any JSON payload and logs it for debugging purposes.
    Useful for testing different webhook formats or integrating with external systems.
    
    Args:
        payload: Any JSON payload
        
    Returns:
        dict: Confirmation of webhook receipt
    """
    logger = request.app.state.logger
    
    task_id = payload.get("task_id", "unknown")
    status = payload.get("status", "unknown")
    
    logger.info(f"Custom webhook received for task {task_id} with status {status}")
    logger.info(f"Custom webhook payload: {json.dumps(payload, indent=2)}")
    
    return {
        "status": "received",
        "message": "Custom webhook processed successfully",
        "received_at": datetime.utcnow().isoformat(),
        "payload_keys": list(payload.keys())
    }


@router.get("/webhook/validate", summary="Validate webhook URL")
async def validate_webhook_url(
    webhook_url: str,
    request: Request
):
    """
    Validate a webhook URL by sending a test payload.
    
    This endpoint helps validate webhook URLs before using them in processing jobs.
    It sends a test payload to the provided URL and reports the response.
    
    Args:
        webhook_url: The webhook URL to validate
        
    Returns:
        dict: Validation result
    """
    import requests
    from requests.exceptions import RequestException
    
    logger = request.app.state.logger
    logger.info(f"Validating webhook URL: {webhook_url}")
    
    # Create test payload
    test_payload = {
        "task_id": "test-validation-12345",
        "status": "completed",
        "timestamp": datetime.utcnow().isoformat(),
        "data": {
            "test": True,
            "message": "This is a test webhook validation"
        },
        "processing_time": 0.1
    }
    
    try:
        # Send test webhook
        response = requests.post(
            webhook_url,
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        return {
            "status": "valid",
            "webhook_url": webhook_url,
            "response_code": response.status_code,
            "response_headers": dict(response.headers),
            "response_body": response.text[:500] if response.text else None,
            "message": "Webhook URL is reachable and responding"
        }
        
    except RequestException as e:
        logger.warning(f"Webhook validation failed for {webhook_url}: {str(e)}")
        return {
            "status": "invalid",
            "webhook_url": webhook_url,
            "error": str(e),
            "message": "Webhook URL is not reachable or not responding correctly"
        }
    except Exception as e:
        logger.error(f"Unexpected error validating webhook {webhook_url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error validating webhook: {str(e)}")


@router.post("/webhook/retry/{task_id}", summary="Retry webhook notification")
async def retry_webhook_notification(
    task_id: str,
    webhook_url: str,
    request: Request
):
    """
    Manually retry webhook notification for a completed job.
    
    This endpoint allows manual retry of webhook notifications for jobs that
    may have failed to send webhooks or for testing purposes.
    
    Args:
        task_id: The task identifier
        webhook_url: The webhook URL to send notification to
        
    Returns:
        dict: Retry result
    """
    from utils.job_tracker import JobTracker
    from tasks.document_processing import send_webhook
    import os
    
    logger = request.app.state.logger
    logger.info(f"Manual webhook retry requested for task {task_id}")
    
    # Initialize job tracker
    job_tracker = JobTracker(
        redis_url=os.getenv("REDIS_URL", "redis://localhost:6379/0")
    )
    
    # Get job status and result
    job_status = job_tracker.get_job_status(task_id)
    job_result = job_tracker.get_job_result(task_id)
    
    if not job_status:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if job_status.status not in ["completed", "failed"]:
        raise HTTPException(status_code=400, detail="Job is not in a final state")
    
    try:
        # Prepare webhook data
        if job_status.status == "completed" and job_result:
            webhook_data = job_result.result
        else:
            webhook_data = {"error": job_status.error or "Job failed"}
        
        # Send webhook using Celery task
        webhook_task = send_webhook.delay(
            webhook_url=webhook_url,
            task_id=task_id,
            status=job_status.status,
            data=webhook_data
        )
        
        return {
            "status": "queued",
            "message": f"Webhook retry queued for task {task_id}",
            "webhook_task_id": webhook_task.id,
            "webhook_url": webhook_url
        }
        
    except Exception as e:
        logger.error(f"Error retrying webhook for task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrying webhook: {str(e)}")


# Webhook configuration and management endpoints

@router.get("/webhook/config", summary="Get webhook configuration")
async def get_webhook_config():
    """
    Get current webhook configuration and best practices.
    
    Returns:
        dict: Webhook configuration information
    """
    return {
        "webhook_requirements": {
            "method": "POST",
            "content_type": "application/json",
            "timeout": "30 seconds",
            "expected_response": "2xx status code"
        },
        "payload_format": {
            "task_id": "string - unique task identifier",
            "status": "string - completed|failed",
            "timestamp": "string - ISO 8601 timestamp",
            "data": "object - result data or error information",
            "processing_time": "number - processing time in seconds (optional)"
        },
        "retry_policy": {
            "max_retries": 3,
            "retry_delay": "60 seconds",
            "backoff": "exponential"
        },
        "security_recommendations": [
            "Use HTTPS URLs for webhook endpoints",
            "Implement webhook signature verification",
            "Validate incoming webhook payloads",
            "Use proper authentication/authorization",
            "Log webhook events for monitoring"
        ],
        "test_endpoints": {
            "test_webhook": "/webhook/test",
            "custom_webhook": "/webhook/custom",
            "validate_url": "/webhook/validate?webhook_url=YOUR_URL"
        }
    }
