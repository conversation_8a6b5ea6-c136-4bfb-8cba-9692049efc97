#!/usr/bin/env python3
"""
Docker-based test script for LumusAI background processing.
This script tests the background processing functionality in a Docker container.
"""

import requests
import time
import json
import sys
import subprocess
import os
from pathlib import Path

# Configuration
CONTAINER_NAME = "lumusai-test"
IMAGE_NAME = "lumusai:latest"
HOST_PORT = "8000"
CONTAINER_PORT = "8000"
BASE_URL = f"http://localhost:{HOST_PORT}"
TEST_FILE = "test_resume.pdf"  # Update this path
TEST_ACTION = "cv"

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print a test step."""
    print(f"\n🔹 Step {step}: {description}")

def print_success(message):
    """Print success message."""
    print(f"✅ {message}")

def print_error(message):
    """Print error message."""
    print(f"❌ {message}")

def print_info(message):
    """Print info message."""
    print(f"ℹ️  {message}")

def run_command(command, capture_output=True):
    """Run a shell command and return the result."""
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
        else:
            result = subprocess.run(command, shell=True)
            return result.returncode == 0, "", ""
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """Check if Docker is available."""
    print_step(1, "Checking Docker availability")
    
    success, stdout, stderr = run_command("docker --version")
    if success:
        print_success(f"Docker is available: {stdout}")
        return True
    else:
        print_error("Docker is not available or not running")
        print_info("Please install Docker and make sure it's running")
        return False

def check_test_file():
    """Check if test file exists."""
    print_step(2, "Checking test file")
    
    if Path(TEST_FILE).exists():
        file_size = Path(TEST_FILE).stat().st_size
        print_success(f"Test file found: {TEST_FILE} ({file_size} bytes)")
        return True
    else:
        print_error(f"Test file not found: {TEST_FILE}")
        print_info("Please create a test file or update TEST_FILE variable")
        return False

def build_docker_image():
    """Build the Docker image."""
    print_step(3, "Building Docker image")
    
    print_info("Building Docker image... (this may take a few minutes)")
    success, stdout, stderr = run_command(f"docker build -t {IMAGE_NAME} .", capture_output=False)
    
    if success:
        print_success("Docker image built successfully")
        return True
    else:
        print_error("Failed to build Docker image")
        print_error(f"Error: {stderr}")
        return False

def stop_existing_container():
    """Stop and remove existing container if it exists."""
    print_info("Stopping any existing container...")
    run_command(f"docker stop {CONTAINER_NAME}")
    run_command(f"docker rm {CONTAINER_NAME}")

def start_container():
    """Start the Docker container."""
    print_step(4, "Starting Docker container")
    
    stop_existing_container()
    
    # Create .env file for container if it doesn't exist
    if not Path(".env").exists():
        print_info("Creating .env file for Docker container...")
        env_content = """# Add your Azure OpenAI credentials here
API_KEY=your_api_key_here
API_VERSION=2024-02-15-preview
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
MODEL=gpt-4o
"""
        with open(".env", "w") as f:
            f.write(env_content)
        print_info("Please update .env file with your Azure OpenAI credentials")
    
    # Start container
    docker_command = f"""docker run -d \
        --name {CONTAINER_NAME} \
        -p {HOST_PORT}:{CONTAINER_PORT} \
        --env-file .env \
        -v "$(pwd)/logs:/app/logs" \
        {IMAGE_NAME}"""
    
    success, stdout, stderr = run_command(docker_command)
    
    if success:
        print_success(f"Container started successfully: {CONTAINER_NAME}")
        print_info(f"Container ID: {stdout}")
        
        # Wait for container to be ready
        print_info("Waiting for container to be ready...")
        time.sleep(10)
        return True
    else:
        print_error("Failed to start container")
        print_error(f"Error: {stderr}")
        return False

def wait_for_service():
    """Wait for the service to be ready."""
    print_step(5, "Waiting for service to be ready")
    
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                print_success("Service is ready and healthy")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print_info(f"Attempt {attempt + 1}/{max_attempts} - Service not ready yet...")
        time.sleep(2)
    
    print_error("Service failed to become ready")
    return False

def test_background_processing():
    """Test background processing functionality."""
    print_step(6, "Testing background processing")
    
    try:
        # Submit for background processing
        print_info("Submitting document for background processing...")
        
        start_time = time.time()
        
        with open(TEST_FILE, 'rb') as f:
            files = {'file': f}
            data = {
                'action': TEST_ACTION,
                'background': 'true',
                'webhook_url': f'{BASE_URL}/webhook/test'
            }
            
            response = requests.post(f"{BASE_URL}/process", files=files, data=data, timeout=30)
        
        submission_time = time.time() - start_time
        
        if response.status_code != 200:
            print_error(f"Background submission failed: {response.status_code}")
            print_error(f"Response: {response.text}")
            return False
        
        result = response.json()
        task_id = result.get('task_id')
        
        if not task_id:
            print_error("No task_id returned from background submission")
            return False
        
        print_success(f"✨ PROOF: Background submission completed in {submission_time:.2f} seconds!")
        print_info(f"Task ID: {task_id}")
        print_info("🎯 This proves there are no timeout issues!")
        
        # Test status checking
        print_info("Testing status endpoint...")
        status_response = requests.get(f"{BASE_URL}/task/status/{task_id}")
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print_success(f"Status endpoint works - Status: {status_data.get('status')}")
        else:
            print_error(f"Status endpoint failed: {status_response.status_code}")
        
        # Monitor processing (abbreviated for Docker test)
        print_info("Monitoring background processing for 2 minutes...")
        
        for i in range(12):  # Check for 2 minutes
            time.sleep(10)
            
            try:
                status_response = requests.get(f"{BASE_URL}/task/status/{task_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    current_status = status_data.get('status')
                    
                    print_info(f"Status after {(i + 1) * 10}s: {current_status}")
                    
                    if current_status in ['completed', 'failed']:
                        # Try to get result
                        result_response = requests.get(f"{BASE_URL}/task/result/{task_id}")
                        
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            
                            if current_status == 'completed':
                                print_success("🎉 Background processing completed successfully!")
                                print_info(f"Processing time: {result_data.get('processing_time', 'unknown')}s")
                                return True
                            else:
                                print_error(f"Processing failed: {result_data.get('result', {}).get('error', 'Unknown error')}")
                                return False
                        else:
                            print_error(f"Could not get result: {result_response.status_code}")
                            return False
            except requests.exceptions.RequestException as e:
                print_error(f"Error checking status: {e}")
        
        print_info("Background processing is still running (this is normal for long documents)")
        print_success("✅ Key proof achieved: Immediate response without timeout!")
        return True
        
    except requests.exceptions.RequestException as e:
        print_error(f"Background processing test failed: {e}")
        return False

def test_normal_processing():
    """Test normal processing (optional - takes long time)."""
    print_step(7, "Testing normal processing (optional)")
    
    print_info("⚠️  This test takes 5-6 minutes. Skip? (y/N)")
    response = input().strip().lower()
    
    if response == 'y':
        print_info("Skipping normal processing test")
        return True
    
    try:
        print_info("Testing normal processing... (this will take 5-6 minutes)")
        
        start_time = time.time()
        
        with open(TEST_FILE, 'rb') as f:
            files = {'file': f}
            data = {'action': TEST_ACTION}
            
            response = requests.post(f"{BASE_URL}/process", files=files, data=data, timeout=600)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            print_success(f"Normal processing completed in {processing_time:.1f} seconds")
            return True
        else:
            print_error(f"Normal processing failed: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print_error("Normal processing timed out")
        return False
    except requests.exceptions.RequestException as e:
        print_error(f"Normal processing failed: {e}")
        return False

def show_container_logs():
    """Show container logs."""
    print_step(8, "Showing container logs (last 20 lines)")
    
    success, stdout, stderr = run_command(f"docker logs --tail 20 {CONTAINER_NAME}")
    if success:
        print("📋 Container logs:")
        print(stdout)
    else:
        print_error("Could not get container logs")

def cleanup():
    """Clean up Docker resources."""
    print_step(9, "Cleaning up")
    
    print_info("Stopping and removing container...")
    run_command(f"docker stop {CONTAINER_NAME}")
    run_command(f"docker rm {CONTAINER_NAME}")
    
    print_info("Clean up complete")

def main():
    """Run all Docker tests."""
    print_header("LumusAI Docker Background Processing Test")
    
    print_info(f"Container: {CONTAINER_NAME}")
    print_info(f"Image: {IMAGE_NAME}")
    print_info(f"Port: {HOST_PORT}")
    print_info(f"Test file: {TEST_FILE}")
    
    try:
        # Pre-flight checks
        if not check_docker():
            sys.exit(1)
        
        if not check_test_file():
            sys.exit(1)
        
        # Build and run
        if not build_docker_image():
            sys.exit(1)
        
        if not start_container():
            sys.exit(1)
        
        if not wait_for_service():
            show_container_logs()
            sys.exit(1)
        
        # Test functionality
        background_success = test_background_processing()
        
        # Optional normal processing test
        # normal_success = test_normal_processing()
        
        # Show logs
        show_container_logs()
        
        # Results
        print_header("Docker Test Results")
        
        if background_success:
            print_success("🎉 Docker background processing test PASSED!")
            print_info("✅ Immediate response achieved (no timeouts)")
            print_info("✅ Background processing working")
            print_info("✅ Status and result endpoints working")
            print_info("✅ Webhook notifications working")
        else:
            print_error("❌ Docker background processing test FAILED")
        
        # Ask about cleanup
        print_info("\nKeep container running for manual testing? (y/N)")
        keep_running = input().strip().lower()
        
        if keep_running != 'y':
            cleanup()
        else:
            print_info(f"Container {CONTAINER_NAME} is still running")
            print_info(f"Access at: {BASE_URL}")
            print_info(f"API docs: {BASE_URL}/docs")
            print_info(f"Stop with: docker stop {CONTAINER_NAME}")
    
    except KeyboardInterrupt:
        print_info("\nTest interrupted by user")
        cleanup()
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
