import asyncio
import time
from fastapi import APIRouter, HTTPException, UploadFile, Form, File, Request

from services.processors.smarthr.cv_processor_test import CVProcessorTest
from services.processors.papirus.tutela_contestacion_processor import TutelaContestacionProcessor
from services.processors.papirus.tutela_fallo_processor import TutelaFalloProcessor
from services.processors.papirus.tutela_desacato_processor import TutelaDesacatoProcessor
from services.processors.papirus.tutela_correo_processor import TutelaCorreoProcessor
from services.processors.facturius.invoice_processor_test import InvoiceProcessorTest
from utils.webhook_sender import webhook_sender
from utils.simple_background import simple_background

router = APIRouter()


def get_processor_mapping(openai_client, langchain_client):
    """
    Returns a mapping of action names to their corresponding processor
    instances.
    """

    return {
        #"invoice": InvoiceProcessor(openai_client, langchain_client),
        "invoice": InvoiceProcessorTest(openai_client, langchain_client),
        "cv": CVProcessorTest(openai_client, langchain_client),
        "tutela_contestacion": TutelaContestacionProcessor(openai_client, langchain_client),
        "tutela_correo_contestacion": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_fallo": TutelaFalloProcessor(openai_client, langchain_client),
        "tutela_correo_fallo": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_desacato": TutelaDesacatoProcessor(openai_client, langchain_client),
        "tutela_correo_desacato": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_correo": TutelaCorreoProcessor(openai_client, langchain_client),
    }


@router.post("/process-test", summary="Process a document with background processing (TEST)")
async def process_request_test(
    request: Request,
    action: str = Form(..., description="Document type (e.g., invoice, cv)"),
    file: UploadFile = File(None, description="File for processing"),
    data: str = Form(None, description="Text or URL to process"),
    webhook_url: str = Form(None, description="Optional webhook URL for completion notification"),
    background: bool = Form(True, description="Process in background (returns immediately)")
):
    """
    TEST ROUTE: Process a document with background processing (returns immediately).

    This is the new background processing route that eliminates timeout issues.
    It returns a task_id immediately and processes the document in the background.

    - **Parameters**:
      - **action**: Document type or action name (e.g., 'invoice', 'cv').
      - **file**: File to process if no text is provided.
      - **data**: Text or URL to process if no file is provided.
      - **webhook_url**: Optional URL to receive completion notification.
      - **background**: Always True for this test route.

    - **Constraints**:
      Only one of `file` or `data` can be provided. If both are given, the
      request fails.

    - **Returns**:
      A JSON object with task_id for status checking and result retrieval.

    Example using cURL:
    ```
    # Background processing (returns immediately - no timeouts!)
    curl -X POST "http://localhost:8000/process-test" \
      -F "action=cv" \
      -F "file=@/path/to/resume.pdf" \
      -F "webhook_url=https://your-app.com/webhook"
    ```
    """

    logger = request.app.state.logger
    logger.info(f"Process request initiated with action: {action}, background: {background}")

    if not file and not data:
        logger.warning("No file or data provided.")
        raise HTTPException(status_code=400, detail="A file or text is required.")
    if file and data:
        logger.warning("Both file and data provided. Only one allowed.")
        raise HTTPException(status_code=400, detail="Send either a text or a file, not both.")

    action = action.lower()

    openai_client = request.app.state.openai_client
    langchain_client = request.app.state.langchain_client

    processor_mapping = get_processor_mapping(openai_client, langchain_client)
    processor = processor_mapping.get(action)
    if not processor:
        raise HTTPException(status_code=400, detail="Invalid Action.")

    # BACKGROUND PROCESSING - Return immediately
    if background:
        try:
            task_id = await simple_background.submit_task(
                action=action,
                processor=processor,
                file=file,
                data=data,
                webhook_url=webhook_url,
                logger=logger
            )

            return {
                "task_id": task_id,
                "status": "processing",
                "message": "Document processing started in background",
                "check_status": f"/task/status/{task_id}",
                "get_result": f"/task/result/{task_id}",
                "webhook_url": webhook_url,
                "estimated_completion": "2-5 minutes"
            }
        except Exception as e:
            logger.error(f"Error starting background task: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error starting background processing: {str(e)}")

    # NORMAL PROCESSING - Your existing logic continues below...

    semaphore = request.app.state.semaphore
    active_tasks = request.app.state.active_tasks

    async def handle_request_with_semaphore(processor, file, data):
        """
        Handle request using a semaphore to limit concurrency.
        """
        async with semaphore:
            try:
                result = await processor.process(file, data)
                return result
            except asyncio.CancelledError:
                logger.warning("Task was cancelled during processing")
                raise  # Re-raise to ensure proper cleanup
            except Exception as e:
                logger.error(f"Error processing action: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error processing the request: {str(e)}")

    # Create a unique task ID for tracking
    task_id = f"{action}-{time.time()}-{id(processor)}"

    # Create the task with proper error handling
    task = asyncio.create_task(handle_request_with_semaphore(processor, file, data))

    # Store task with metadata for better tracking
    active_tasks[task] = {
        "action": action,
        "task_id": task_id,
        "start_time": time.time(),
        "file_name": getattr(file, "filename", None) or str(data)[:30] if data else "No file",
        "webhook_url": webhook_url
    }

    # Add done callback to ensure task is removed even if the main code path fails
    def task_done_callback(completed_task):
        try:
            # Check if task is still in active_tasks (might have been removed already)
            if completed_task in active_tasks:
                task_info = active_tasks.pop(completed_task, {"action": "unknown"})
                duration = time.time() - task_info.get("start_time", time.time())
                task_webhook_url = task_info.get("webhook_url")
                task_id_for_webhook = task_info.get("task_id", "unknown")

                logger.info(f"Task {task_id_for_webhook} ({task_info['action']}) completed in {duration:.2f}s")

                # Check for exceptions and send appropriate webhook
                if completed_task.exception():
                    error = completed_task.exception()
                    logger.error(f"Task {task_id_for_webhook} failed with error: {error}")

                    # Send failure webhook if URL provided
                    if task_webhook_url:
                        webhook_sender.send_webhook_background(
                            webhook_url=task_webhook_url,
                            task_id=task_id_for_webhook,
                            status="failed",
                            data={"error": str(error)},
                            processing_time=duration
                        )
                else:
                    # Task completed successfully - webhook will be sent in the main try block
                    # with the actual result data
                    pass

        except Exception as e:
            logger.error(f"Error in task cleanup callback: {e}")

    # Add the callback
    task.add_done_callback(task_done_callback)

    try:
        # Wait for the task to complete
        start_time = time.time()
        result = await task
        processing_time = time.time() - start_time

        # Send success webhook if URL provided
        if webhook_url:
            webhook_sender.send_webhook_background(
                webhook_url=webhook_url,
                task_id=task_id,
                status="completed",
                data=result,
                processing_time=processing_time
            )

        return result
    except asyncio.CancelledError:
        # Handle cancellation gracefully
        logger.warning(f"Task {task_id} was cancelled")

        # Send cancellation webhook if URL provided
        if webhook_url:
            webhook_sender.send_webhook_background(
                webhook_url=webhook_url,
                task_id=task_id,
                status="cancelled",
                data={"error": "Task was cancelled"},
                processing_time=0
            )

        raise HTTPException(status_code=499, detail="Request cancelled")
    except Exception as e:
        # Log any unexpected exceptions
        logger.error(f"Unexpected error in task {task_id}: {str(e)}")

        # Send error webhook if URL provided
        if webhook_url:
            webhook_sender.send_webhook_background(
                webhook_url=webhook_url,
                task_id=task_id,
                status="failed",
                data={"error": str(e)},
                processing_time=0
            )

        raise
    finally:
        # This is a backup to ensure the task is removed from active_tasks
        # The done_callback should handle this in most cases
        if task in active_tasks:
            active_tasks.pop(task, None)


# Simple endpoints for background task management
@router.get("/task/status/{task_id}", summary="Get background task status")
async def get_background_task_status(task_id: str, request: Request):
    """
    Get the status of a background processing task.

    Returns:
        dict: Task status information
    """
    logger = request.app.state.logger
    logger.info(f"Status check for task: {task_id}")

    task_info = simple_background.get_task_status(task_id)
    if not task_info:
        raise HTTPException(status_code=404, detail="Task not found")

    return task_info


@router.get("/task/result/{task_id}", summary="Get background task result")
async def get_background_task_result(task_id: str, request: Request):
    """
    Get the result of a completed background processing task.

    Returns:
        dict: Task result or error information
    """
    logger = request.app.state.logger
    logger.info(f"Result requested for task: {task_id}")

    task_info = simple_background.get_task_status(task_id)
    if not task_info:
        raise HTTPException(status_code=404, detail="Task not found")

    if task_info["status"] == "processing":
        raise HTTPException(status_code=202, detail="Task is still processing")

    result = simple_background.get_task_result(task_id)
    if result is None:
        raise HTTPException(status_code=404, detail="Task result not available")

    return {
        "task_id": task_id,
        "status": task_info["status"],
        "result": result,
        "processing_time": task_info.get("processing_time", 0)
    }


@router.get("/tasks/list", summary="List all background tasks")
async def list_background_tasks(request: Request):
    """List all background tasks for monitoring."""
    logger = request.app.state.logger
    logger.info("Background tasks list requested")

    return simple_background.list_tasks()


# Original synchronous processing route (restored)
@router.post("/process", summary="Process a document (original synchronous)")
async def process_request_original(
    request: Request,
    action: str = Form(..., description="Document type (e.g., invoice, cv)"),
    file: UploadFile = File(None, description="File for processing"),
    data: str = Form(None, description="Text or URL to process"),
    webhook_url: str = Form(None, description="Optional webhook URL for completion notification")
):
    """
    ORIGINAL ROUTE: Process a document synchronously (client waits for completion).

    This is the original processing route that works exactly as before.
    The client waits 5-6 minutes for processing to complete.

    - **Parameters**:
      - **action**: Document type or action name (e.g., 'invoice', 'cv').
      - **file**: File to process if no text is provided.
      - **data**: Text or URL to process if no file is provided.
      - **webhook_url**: Optional URL to receive completion notification.

    - **Constraints**:
      Only one of `file` or `data` can be provided. If both are given, the
      request fails.

    - **Returns**:
      A JSON object with the processed information (after 5-6 minutes).

    Example using cURL:
    ```
    # Original synchronous processing (waits 5-6 minutes)
    curl -X POST "http://localhost:8000/process" \
      -F "action=cv" \
      -F "file=@/path/to/resume.pdf"
    ```
    """

    logger = request.app.state.logger
    logger.info(f"Original synchronous process request initiated with action: {action}")

    if not file and not data:
        logger.warning("No file or data provided.")
        raise HTTPException(status_code=400, detail="A file or text is required.")
    if file and data:
        logger.warning("Both file and data provided. Only one allowed.")
        raise HTTPException(status_code=400, detail="Send either a text or a file, not both.")

    action = action.lower()

    openai_client = request.app.state.openai_client
    langchain_client = request.app.state.langchain_client

    processor_mapping = get_processor_mapping(openai_client, langchain_client)
    processor = processor_mapping.get(action)
    if not processor:
        raise HTTPException(status_code=400, detail="Invalid Action.")

    semaphore = request.app.state.semaphore
    active_tasks = request.app.state.active_tasks

    async def handle_request_with_semaphore(processor, file, data):
        """
        Handle request using a semaphore to limit concurrency.
        """
        async with semaphore:
            try:
                result = await processor.process(file, data)
                return result
            except asyncio.CancelledError:
                logger.warning("Task was cancelled during processing")
                raise  # Re-raise to ensure proper cleanup
            except Exception as e:
                logger.error(f"Error processing action: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Error processing the request: {str(e)}")

    # Create a unique task ID for tracking
    task_id = f"{action}-{time.time()}-{id(processor)}"

    # Create the task with proper error handling
    task = asyncio.create_task(handle_request_with_semaphore(processor, file, data))

    # Store task with metadata for better tracking
    active_tasks[task] = {
        "action": action,
        "task_id": task_id,
        "start_time": time.time(),
        "file_name": getattr(file, "filename", None) or str(data)[:30] if data else "No file",
        "webhook_url": webhook_url
    }

    # Add done callback to ensure task is removed even if the main code path fails
    def task_done_callback(completed_task):
        try:
            # Check if task is still in active_tasks (might have been removed already)
            if completed_task in active_tasks:
                task_info = active_tasks.pop(completed_task, {"action": "unknown"})
                duration = time.time() - task_info.get("start_time", time.time())
                task_webhook_url = task_info.get("webhook_url")
                task_id_for_webhook = task_info.get("task_id", "unknown")

                logger.info(f"Task {task_id_for_webhook} ({task_info['action']}) completed in {duration:.2f}s")

                # Check for exceptions and send appropriate webhook
                if completed_task.exception():
                    error = completed_task.exception()
                    logger.error(f"Task {task_id_for_webhook} failed with error: {error}")

                    # Send failure webhook if URL provided
                    if task_webhook_url:
                        webhook_sender.send_webhook_background(
                            webhook_url=task_webhook_url,
                            task_id=task_id_for_webhook,
                            status="failed",
                            data={"error": str(error)},
                            processing_time=duration
                        )
                else:
                    # Task completed successfully - webhook will be sent in the main try block
                    # with the actual result data
                    pass

        except Exception as e:
            logger.error(f"Error in task cleanup callback: {e}")

    # Add the callback
    task.add_done_callback(task_done_callback)

    try:
        # Wait for the task to complete
        start_time = time.time()
        result = await task
        processing_time = time.time() - start_time

        # Send success webhook if URL provided
        if webhook_url:
            webhook_sender.send_webhook_background(
                webhook_url=webhook_url,
                task_id=task_id,
                status="completed",
                data=result,
                processing_time=processing_time
            )

        return result
    except asyncio.CancelledError:
        # Handle cancellation gracefully
        logger.warning(f"Task {task_id} was cancelled")

        # Send cancellation webhook if URL provided
        if webhook_url:
            webhook_sender.send_webhook_background(
                webhook_url=webhook_url,
                task_id=task_id,
                status="cancelled",
                data={"error": "Task was cancelled"},
                processing_time=0
            )

        raise HTTPException(status_code=499, detail="Request cancelled")
    except Exception as e:
        # Log any unexpected exceptions
        logger.error(f"Unexpected error in task {task_id}: {str(e)}")

        # Send error webhook if URL provided
        if webhook_url:
            webhook_sender.send_webhook_background(
                webhook_url=webhook_url,
                task_id=task_id,
                status="failed",
                data={"error": str(e)},
                processing_time=0
            )

        raise
    finally:
        # This is a backup to ensure the task is removed from active_tasks
        # The done_callback should handle this in most cases
        if task in active_tasks:
            active_tasks.pop(task, None)
