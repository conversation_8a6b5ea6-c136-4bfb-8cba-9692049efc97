import asyncio
import time
from fastapi import APIRouter, HTTPException, UploadFile, Form, File, Request

from services.processors.smarthr.cv_processor_test import CVProcessorTest
from services.processors.papirus.tutela_contestacion_processor import TutelaContestacionProcessor
from services.processors.papirus.tutela_fallo_processor import TutelaFalloProcessor
from services.processors.papirus.tutela_desacato_processor import TutelaDesacatoProcessor
from services.processors.papirus.tutela_correo_processor import TutelaCorreoProcessor
from services.processors.facturius.invoice_processor_test import InvoiceProcessorTest
from utils.webhook_sender import webhook_sender
from utils.background_processor import background_processor

router = APIRouter()


def get_processor_mapping(openai_client, langchain_client):
    """
    Returns a mapping of action names to their corresponding processor
    instances.
    """

    return {
        #"invoice": InvoiceProcessor(openai_client, langchain_client),
        "invoice": InvoiceProcessorTest(openai_client, langchain_client),
        "cv": CVProcessorTest(openai_client, langchain_client),
        "tutela_contestacion": TutelaContestacionProcessor(openai_client, langchain_client),
        "tutela_correo_contestacion": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_fallo": TutelaFalloProcessor(openai_client, langchain_client),
        "tutela_correo_fallo": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_desacato": TutelaDesacatoProcessor(openai_client, langchain_client),
        "tutela_correo_desacato": TutelaCorreoProcessor(openai_client, langchain_client),
        "tutela_correo": TutelaCorreoProcessor(openai_client, langchain_client),
    }


@router.post("/process", summary="Process a document (returns immediately)")
async def process_request(
    request: Request,
    action: str = Form(..., description="Document type (e.g., invoice, cv)"),
    file: UploadFile = File(None, description="File for processing"),
    data: str = Form(None, description="Text or URL to process"),
    webhook_url: str = Form(None, description="Optional webhook URL for completion notification")
):
    """
    Submit a document for background processing and return immediately with a task ID.

    This endpoint no longer blocks waiting for processing to complete. Instead, it:
    1. Returns immediately with a task ID
    2. Processes the document in the background
    3. Sends a webhook notification when complete (if webhook_url provided)

    - **Parameters**:
      - **action**: Document type or action name (e.g., 'invoice', 'cv').
      - **file**: File to process if no text is provided.
      - **data**: Text or URL to process if no file is provided.
      - **webhook_url**: Optional URL to receive completion notification.

    - **Constraints**:
      Only one of `file` or `data` can be provided. If both are given, the
      request fails.

    - **Returns**:
      A JSON object with task ID and status checking information.

    Example using cURL:
    ```
    curl -X POST "http://localhost:8000/process" \
      -F "action=cv" \
      -F "file=@/path/to/resume.pdf" \
      -F "webhook_url=https://your-app.com/webhook"
    ```

    Response:
    ```json
    {
      "task_id": "cv-1703123456-abc12345",
      "status": "processing",
      "message": "Document processing started in background",
      "status_url": "/task/status/cv-1703123456-abc12345",
      "result_url": "/task/result/cv-1703123456-abc12345"
    }
    ```
    """

    logger = request.app.state.logger
    logger.info(f"Background process request initiated with action: {action}")

    if not file and not data:
        logger.warning("No file or data provided.")
        raise HTTPException(status_code=400, detail="A file or text is required.")
    if file and data:
        logger.warning("Both file and data provided. Only one allowed.")
        raise HTTPException(status_code=400, detail="Send either a text or a file, not both.")

    action = action.lower()

    openai_client = request.app.state.openai_client
    langchain_client = request.app.state.langchain_client

    processor_mapping = get_processor_mapping(openai_client, langchain_client)
    processor = processor_mapping.get(action)
    if not processor:
        raise HTTPException(status_code=400, detail="Invalid Action.")

    try:
        # Submit task for background processing
        task_id = await background_processor.submit_task(
            action=action,
            processor=processor,
            file=file,
            data=data,
            webhook_url=webhook_url,
            logger=logger
        )

        # Return immediately with task information
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "Document processing started in background",
            "status_url": f"/task/status/{task_id}",
            "result_url": f"/task/result/{task_id}",
            "webhook_url": webhook_url,
            "estimated_completion": "2-5 minutes"
        }

    except Exception as e:
        logger.error(f"Error submitting background task: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error starting background processing: {str(e)}")


@router.get("/task/status/{task_id}", summary="Get task status")
async def get_task_status(task_id: str, request: Request):
    """
    Get the current status of a background processing task.

    Args:
        task_id: The task identifier returned from /process

    Returns:
        dict: Task status information

    Example:
    ```
    curl -X GET "http://localhost:8000/task/status/cv-1703123456-abc12345"
    ```
    """
    logger = request.app.state.logger
    logger.info(f"Status check for task: {task_id}")

    task_info = background_processor.get_task_status(task_id)

    if not task_info:
        raise HTTPException(status_code=404, detail="Task not found")

    return task_info


@router.get("/task/result/{task_id}", summary="Get task result")
async def get_task_result(task_id: str, request: Request):
    """
    Get the result of a completed background processing task.

    Args:
        task_id: The task identifier returned from /process

    Returns:
        dict: Task result data or error information

    Example:
    ```
    curl -X GET "http://localhost:8000/task/result/cv-1703123456-abc12345"
    ```
    """
    logger = request.app.state.logger
    logger.info(f"Result requested for task: {task_id}")

    task_info = background_processor.get_task_status(task_id)

    if not task_info:
        raise HTTPException(status_code=404, detail="Task not found")

    if task_info["status"] == "processing":
        raise HTTPException(status_code=202, detail="Task is still processing")

    result = background_processor.get_task_result(task_id)

    if result is None:
        raise HTTPException(status_code=404, detail="Task result not found")

    return {
        "task_id": task_id,
        "status": task_info["status"],
        "result": result,
        "processing_time": task_info.get("processing_time", 0)
    }


@router.get("/tasks", summary="List all tasks (monitoring)")
async def list_all_tasks(request: Request):
    """
    List all tasks for monitoring purposes.

    Returns:
        dict: All active and completed tasks
    """
    logger = request.app.state.logger
    logger.info("All tasks requested")

    return background_processor.get_all_tasks()


@router.post("/tasks/cleanup", summary="Clean up old completed tasks")
async def cleanup_old_tasks(request: Request, max_age_hours: int = 24):
    """
    Clean up completed tasks older than specified hours.

    Args:
        max_age_hours: Maximum age in hours for completed tasks

    Returns:
        dict: Cleanup summary
    """
    logger = request.app.state.logger
    logger.info(f"Cleanup requested for tasks older than {max_age_hours} hours")

    cleaned_count = background_processor.cleanup_old_tasks(max_age_hours)

    return {
        "status": "completed",
        "message": f"Cleaned up {cleaned_count} old tasks",
        "cleaned_count": cleaned_count
    }
