# 🐳 Docker Testing Guide for LumusAI Background Processing

## 🎯 **Prove Background Processing Works in Docker**

This guide shows you exactly how to test and prove that the background processing eliminates timeout issues when running in Docker.

## 🚀 **Quick Test (Recommended)**

### **Option 1: Automated Test Script**

```bash
# Make script executable
chmod +x docker_test_commands.sh

# Run complete test suite
./docker_test_commands.sh full-test
```

**Expected Result:** 
- ✅ Container builds and starts
- ✅ Background request completes in < 2 seconds
- ✅ Processing continues in background
- ✅ No timeout issues

### **Option 2: Docker Compose Test**

```bash
# 1. Create .env file with your credentials
cp .env.example .env
# Edit .env with your Azure OpenAI credentials

# 2. Run the test
docker-compose -f docker-compose.test.yml up --build

# 3. Run test runner (in another terminal)
docker-compose -f docker-compose.test.yml run --rm test-runner
```

## 🔧 **Manual Step-by-Step Testing**

### **Step 1: Setup**

```bash
# 1. Create environment file
cp .env.example .env

# 2. Edit .env with your Azure OpenAI credentials
# API_KEY=your_actual_api_key
# API_VERSION=2024-02-15-preview
# AZURE_ENDPOINT=https://your-resource.openai.azure.com/
# MODEL=gpt-4o

# 3. Build Docker image
docker build -t lumusai:latest .
```

### **Step 2: Start Container**

```bash
# Start the container
docker run -d \
  --name lumusai-test \
  -p 8000:8000 \
  --env-file .env \
  -v "$(pwd)/logs:/app/logs" \
  lumusai:latest

# Wait for container to be ready
sleep 10

# Check if service is healthy
curl http://localhost:8000/health
```

### **Step 3: Test Background Processing (MAIN PROOF)**

```bash
# Test background processing with immediate response
time curl -X POST "http://localhost:8000/process" \
  -F "action=cv" \
  -F "data=John Doe, Software Engineer, Python, FastAPI, 5 years experience" \
  -F "background=true" \
  -F "webhook_url=http://localhost:8000/webhook/test"
```

**Expected Response (< 2 seconds):**
```json
{
  "task_id": "cv-**********-abc12345",
  "status": "processing",
  "message": "Document processing started in background",
  "check_status": "/task/status/cv-**********-abc12345",
  "get_result": "/task/result/cv-**********-abc12345",
  "webhook_url": "http://localhost:8000/webhook/test",
  "estimated_completion": "2-5 minutes"
}
```

### **Step 4: Monitor Processing**

```bash
# Use the task_id from Step 3
TASK_ID="cv-**********-abc12345"  # Replace with actual task ID

# Check status
curl http://localhost:8000/task/status/$TASK_ID

# Wait and check again
sleep 30
curl http://localhost:8000/task/status/$TASK_ID

# Get result when ready (after 2-5 minutes)
curl http://localhost:8000/task/result/$TASK_ID
```

### **Step 5: Verify Logs**

```bash
# Check container logs for processing activity
docker logs lumusai-test --tail 20

# Look for webhook notifications
docker logs lumusai-test | grep -i webhook
```

## 🧪 **Automated Test Scripts**

### **Python Test Script**

```bash
# Run comprehensive Python test
python test_docker_background.py
```

This script will:
- ✅ Build Docker image
- ✅ Start container
- ✅ Test immediate response (< 2 seconds)
- ✅ Monitor background processing
- ✅ Verify webhook delivery
- ✅ Show proof of no timeouts

### **Shell Script Test**

```bash
# Individual commands
./docker_test_commands.sh build    # Build image
./docker_test_commands.sh start    # Start container
./docker_test_commands.sh test     # Test background processing
./docker_test_commands.sh logs     # Show logs
./docker_test_commands.sh stop     # Stop container
```

## 📊 **Performance Comparison**

### **Before (Normal Processing)**
```bash
# This would take 5-6 minutes and might timeout
time curl -X POST "http://localhost:8000/process" \
  -F "action=cv" \
  -F "data=test data"
# Result: 300+ seconds, potential timeout
```

### **After (Background Processing)**
```bash
# This returns immediately
time curl -X POST "http://localhost:8000/process" \
  -F "action=cv" \
  -F "data=test data" \
  -F "background=true"
# Result: < 2 seconds, no timeout!
```

## 🔍 **What to Look For (Proof Points)**

### **✅ Immediate Response**
- Background requests return in < 2 seconds
- Task ID is provided immediately
- No waiting for processing to complete

### **✅ Background Processing**
- Processing continues after response
- Status endpoint shows "processing"
- Eventually shows "completed" or "failed"

### **✅ Webhook Notifications**
- Webhook sent when processing completes
- Check logs for webhook delivery
- Test endpoint receives notifications

### **✅ No Timeouts**
- Client never waits more than 2 seconds
- Long processing happens in background
- Multiple requests can be submitted simultaneously

## 🐳 **Docker-Specific Benefits**

### **Container Isolation**
- Processing doesn't affect other containers
- Resource limits prevent memory issues
- Clean environment for each deployment

### **Scalability**
- Can run multiple containers
- Load balancer can distribute requests
- Horizontal scaling possible

### **Production Ready**
- Same image for dev/staging/production
- Environment variables for configuration
- Health checks and monitoring

## 🚨 **Troubleshooting**

### **Container Won't Start**
```bash
# Check logs
docker logs lumusai-test

# Check if port is available
netstat -an | grep 8000

# Verify .env file
cat .env
```

### **Service Not Responding**
```bash
# Check container status
docker ps

# Check health endpoint
curl http://localhost:8000/health

# Restart container
docker restart lumusai-test
```

### **Background Processing Fails**
```bash
# Check container logs
docker logs lumusai-test --tail 50

# Verify environment variables
docker exec lumusai-test env | grep API_KEY

# Test with simple data
curl -X POST "http://localhost:8000/process" \
  -F "action=cv" \
  -F "data=simple test" \
  -F "background=true"
```

## 📈 **Success Metrics**

After running tests, you should see:

- ✅ **Response Time**: < 2 seconds for background requests
- ✅ **Task Creation**: Task ID returned immediately
- ✅ **Status Tracking**: Status endpoint works
- ✅ **Background Processing**: Processing completes in background
- ✅ **Webhook Delivery**: Notifications sent when done
- ✅ **No Timeouts**: Zero timeout errors
- ✅ **Container Health**: Service remains responsive

## 🎉 **Cleanup**

```bash
# Stop and remove container
docker stop lumusai-test
docker rm lumusai-test

# Remove image (optional)
docker rmi lumusai:latest

# Or use the script
./docker_test_commands.sh clean
```

---

**🎯 The key proof is that background requests return immediately (< 2 seconds) instead of taking 5-6 minutes, completely eliminating timeout issues while maintaining the same processing quality!**
