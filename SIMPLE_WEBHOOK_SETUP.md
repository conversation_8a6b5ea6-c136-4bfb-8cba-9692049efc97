# 🔔 Simple Webhook Notifications for LumusAI

## ✅ **Problem Solved: No More Waiting for Results!**

Your existing `/process` endpoint now supports webhook notifications. The processing still takes 5-6 minutes, but now your client can:

1. **Submit the request** and get the result normally (for clients that can wait)
2. **Provide a webhook URL** and get notified when processing completes (for clients that can't wait)

## 🚀 **What Changed (Minimal)**

- Added optional `webhook_url` parameter to `/process` endpoint
- When processing completes, sends a POST request to your webhook URL
- **Everything else stays exactly the same**

## 📋 **Installation**

```bash
# Install webhook dependency
pip install aiohttp==3.10.10 aiosignal==1.3.1

# Start your service
uvicorn main:app --reload
```

## 🔧 **Usage**

### **Option 1: Normal Processing (No Change)**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf"
```
- C<PERSON> waits 5-6 minutes
- Gets result directly in response
- **Works exactly like before**

### **Option 2: With Webhook Notification**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "webhook_url=https://your-app.com/webhook"
```
- Client still waits 5-6 minutes for the response
- **PLUS** gets webhook notification when done
- Useful for logging, notifications, or triggering other processes

## 🔔 **Webhook Payload**

When processing completes, your webhook URL receives:

**Success:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "personal_info": { "full_name": "John Doe" },
    "skills": [...],
    "work_experience": [...]
  },
  "processing_time": 245.6
}
```

**Failure:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "failed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "error": "Error processing document: Invalid file format"
  },
  "processing_time": 12.3
}
```

## 🧪 **Testing**

### **Use Built-in Test Webhook**
```bash
# Test with built-in webhook endpoint
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "webhook_url=http://localhost:8000/webhook/test"

# Check logs to see webhook received
tail -f logs/app.log | grep -i webhook
```

### **Create Your Own Webhook Receiver**
```python
from fastapi import FastAPI
import json

app = FastAPI()

@app.post("/webhook")
async def receive_webhook(payload: dict):
    print(f"✅ Document processing completed!")
    print(f"Task ID: {payload['task_id']}")
    print(f"Status: {payload['status']}")
    print(f"Processing time: {payload['processing_time']}s")
    
    if payload['status'] == 'completed':
        # Do something with the results
        result = payload['data']
        print(f"Results received: {len(str(result))} characters")
    else:
        # Handle error
        error = payload['data']['error']
        print(f"Error: {error}")
    
    return {"status": "received"}

# Run with: uvicorn webhook_receiver:app --port 8001
```

## 💡 **Use Cases**

### **1. Email Notifications**
```python
@app.post("/webhook/email-notify")
async def email_notification(payload: dict):
    if payload['status'] == 'completed':
        send_email(
            to="<EMAIL>",
            subject="Document processed successfully",
            body=f"Your {payload['task_id']} is ready!"
        )
    return {"status": "email_sent"}
```

### **2. Database Updates**
```python
@app.post("/webhook/db-update")
async def update_database(payload: dict):
    await db.update_document_status(
        task_id=payload['task_id'],
        status=payload['status'],
        result=payload['data'] if payload['status'] == 'completed' else None
    )
    return {"status": "db_updated"}
```

### **3. Trigger Next Process**
```python
@app.post("/webhook/next-step")
async def trigger_next_process(payload: dict):
    if payload['status'] == 'completed':
        # Start next processing step
        await start_next_process(payload['data'])
    return {"status": "next_process_started"}
```

## 🔒 **Security Notes**

1. **Use HTTPS** for webhook URLs in production
2. **Validate payloads** in your webhook receiver
3. **Add authentication** if needed (API keys, signatures)
4. **Handle failures gracefully** - webhook failures don't affect processing

## 📊 **Monitoring**

Check logs for webhook activity:
```bash
# See webhook sending attempts
tail -f logs/app.log | grep -i webhook

# Look for specific patterns
grep "Webhook" logs/app.log
```

## ✨ **Benefits**

- **✅ Minimal Code Changes** - Your existing processing stays the same
- **✅ Backward Compatible** - Existing clients work unchanged
- **✅ Flexible Integration** - Add webhooks where needed
- **✅ Reliable Delivery** - Automatic retries with exponential backoff
- **✅ Error Handling** - Get notified of both success and failure

## 🚨 **Important Notes**

- **This doesn't solve timeout issues** - clients still wait 5-6 minutes
- **Webhooks are additional notifications** - not a replacement for the response
- **For timeout solutions**, you'd need the full async background processing system

---

**🎯 Perfect for:** Adding notifications, logging, triggering workflows, or integrating with other systems without changing your core processing logic!
