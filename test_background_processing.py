#!/usr/bin/env python3
"""
Test script to prove the new background processing functionality works.
This script tests both normal and background processing modes.
"""

import requests
import time
import json
import sys
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
TEST_FILE = "test_resume.pdf"  # Replace with your test file path
TEST_ACTION = "cv"  # Change to your preferred action

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print a test step."""
    print(f"\n🔹 Step {step}: {description}")

def print_success(message):
    """Print success message."""
    print(f"✅ {message}")

def print_error(message):
    """Print error message."""
    print(f"❌ {message}")

def print_info(message):
    """Print info message."""
    print(f"ℹ️  {message}")

def test_health_check():
    """Test if the service is running."""
    print_step(1, "Testing service health")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print_success("Service is running and healthy")
            return True
        else:
            print_error(f"Service health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"Cannot connect to service: {e}")
        print_info("Make sure the service is running: uvicorn main:app --reload")
        return False

def test_webhook_endpoint():
    """Test the webhook test endpoint."""
    print_step(2, "Testing webhook endpoint")
    
    try:
        test_payload = {
            "task_id": "test-123",
            "status": "completed",
            "data": {"test": True}
        }
        
        response = requests.post(f"{BASE_URL}/webhook/test", json=test_payload, timeout=10)
        if response.status_code == 200:
            print_success("Webhook test endpoint is working")
            return True
        else:
            print_error(f"Webhook test failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"Webhook test failed: {e}")
        return False

def test_normal_processing():
    """Test normal processing mode (existing functionality)."""
    print_step(3, "Testing normal processing mode")
    
    if not Path(TEST_FILE).exists():
        print_error(f"Test file '{TEST_FILE}' not found")
        print_info("Please create a test file or update TEST_FILE variable")
        return False
    
    try:
        print_info("Submitting document for normal processing...")
        print_info("⏳ This will take 5-6 minutes (testing existing functionality)")
        
        start_time = time.time()
        
        with open(TEST_FILE, 'rb') as f:
            files = {'file': f}
            data = {'action': TEST_ACTION}
            
            response = requests.post(f"{BASE_URL}/process", files=files, data=data, timeout=600)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print_success(f"Normal processing completed in {processing_time:.1f} seconds")
            print_info(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Non-dict result'}")
            return True
        else:
            print_error(f"Normal processing failed: {response.status_code}")
            print_error(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print_error("Normal processing timed out (this might be expected)")
        return False
    except requests.exceptions.RequestException as e:
        print_error(f"Normal processing failed: {e}")
        return False

def test_background_processing():
    """Test background processing mode (new functionality)."""
    print_step(4, "Testing background processing mode")
    
    if not Path(TEST_FILE).exists():
        print_error(f"Test file '{TEST_FILE}' not found")
        return False
    
    try:
        # Submit for background processing
        print_info("Submitting document for background processing...")
        
        start_time = time.time()
        
        with open(TEST_FILE, 'rb') as f:
            files = {'file': f}
            data = {
                'action': TEST_ACTION,
                'background': 'true',
                'webhook_url': f'{BASE_URL}/webhook/test'
            }
            
            response = requests.post(f"{BASE_URL}/process", files=files, data=data, timeout=30)
        
        submission_time = time.time() - start_time
        
        if response.status_code != 200:
            print_error(f"Background submission failed: {response.status_code}")
            print_error(f"Response: {response.text}")
            return False
        
        result = response.json()
        task_id = result.get('task_id')
        
        if not task_id:
            print_error("No task_id returned from background submission")
            return False
        
        print_success(f"Background submission completed in {submission_time:.2f} seconds")
        print_info(f"Task ID: {task_id}")
        
        # Test status checking
        print_info("Testing status checking...")
        status_response = requests.get(f"{BASE_URL}/task/status/{task_id}")
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print_success(f"Status check working - Status: {status_data.get('status')}")
        else:
            print_error(f"Status check failed: {status_response.status_code}")
        
        # Monitor processing
        print_info("Monitoring background processing...")
        max_wait_time = 600  # 10 minutes max
        check_interval = 10  # Check every 10 seconds
        
        for i in range(0, max_wait_time, check_interval):
            time.sleep(check_interval)
            
            try:
                status_response = requests.get(f"{BASE_URL}/task/status/{task_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    current_status = status_data.get('status')
                    
                    print_info(f"Status after {i + check_interval}s: {current_status}")
                    
                    if current_status in ['completed', 'failed']:
                        # Try to get result
                        result_response = requests.get(f"{BASE_URL}/task/result/{task_id}")
                        
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            total_time = i + check_interval
                            
                            if current_status == 'completed':
                                print_success(f"Background processing completed in ~{total_time}s")
                                print_info(f"Result available with keys: {list(result_data.get('result', {}).keys()) if isinstance(result_data.get('result'), dict) else 'Non-dict result'}")
                            else:
                                print_error(f"Background processing failed: {result_data.get('result', {}).get('error', 'Unknown error')}")
                            
                            return current_status == 'completed'
                        else:
                            print_error(f"Could not get result: {result_response.status_code}")
                            return False
                else:
                    print_error(f"Status check failed: {status_response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print_error(f"Error checking status: {e}")
        
        print_error("Background processing timed out")
        return False
        
    except requests.exceptions.RequestException as e:
        print_error(f"Background processing test failed: {e}")
        return False

def test_task_listing():
    """Test task listing endpoint."""
    print_step(5, "Testing task listing")
    
    try:
        response = requests.get(f"{BASE_URL}/tasks/list")
        if response.status_code == 200:
            tasks_data = response.json()
            active_count = tasks_data.get('active_count', 0)
            completed_count = tasks_data.get('completed_count', 0)
            
            print_success(f"Task listing works - Active: {active_count}, Completed: {completed_count}")
            return True
        else:
            print_error(f"Task listing failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print_error(f"Task listing failed: {e}")
        return False

def main():
    """Run all tests."""
    print_header("LumusAI Background Processing Test Suite")
    
    print_info(f"Testing against: {BASE_URL}")
    print_info(f"Test file: {TEST_FILE}")
    print_info(f"Test action: {TEST_ACTION}")
    
    # Check if test file exists
    if not Path(TEST_FILE).exists():
        print_error(f"Test file '{TEST_FILE}' not found!")
        print_info("Please:")
        print_info("1. Create a test file (PDF, DOCX, etc.)")
        print_info("2. Update the TEST_FILE variable in this script")
        print_info("3. Update TEST_ACTION if needed")
        sys.exit(1)
    
    tests = [
        ("Service Health", test_health_check),
        ("Webhook Endpoint", test_webhook_endpoint),
        ("Task Listing", test_task_listing),
        ("Background Processing", test_background_processing),
        # ("Normal Processing", test_normal_processing),  # Uncomment to test normal mode
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print_error(f"Test '{test_name}' crashed: {e}")
            results[test_name] = False
    
    # Summary
    print_header("Test Results Summary")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print_success("🎉 All tests passed! Background processing is working correctly!")
    else:
        print_error("❌ Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
