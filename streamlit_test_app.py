#!/usr/bin/env python3
"""
Aplicación Streamlit para probar LumusAI Background Processing.
Esta app emula cómo las aplicaciones reales se conectarían al microservicio.
"""

import streamlit as st
import requests
import time
import json
from datetime import datetime
import threading
from fastapi import FastAPI, Request
import uvicorn
import asyncio

# Configuración para Docker
LUMUSAI_URL = "http://lumusai-app:8000"  # Nombre del contenedor
WEBHOOK_PORT = 8002
WEBHOOK_URL = f"http://streamlit-test:{WEBHOOK_PORT}/webhook"  # Nombre del contenedor Streamlit

# Estado global para webhooks
if 'received_webhooks' not in st.session_state:
    st.session_state.received_webhooks = []
if 'webhook_server_running' not in st.session_state:
    st.session_state.webhook_server_running = False
if 'active_tasks' not in st.session_state:
    st.session_state.active_tasks = {}

# Configuración de la página
st.set_page_config(
    page_title="LumusAI Test Client",
    page_icon="🚀",
    layout="wide"
)

st.title("🚀 LumusAI Background Processing - Test Client")
st.markdown("Esta aplicación emula cómo las apps reales se conectarían a tu microservicio")

# Sidebar para configuración
st.sidebar.header("⚙️ Configuración")

# Detectar si estamos en Docker o local
import os
if os.getenv("LUMUSAI_URL"):
    default_lumusai = os.getenv("LUMUSAI_URL")
    default_webhook = os.getenv("WEBHOOK_URL", WEBHOOK_URL)
    st.sidebar.success("🐳 Ejecutándose en Docker")
else:
    default_lumusai = "http://localhost:8000"
    default_webhook = "http://localhost:8002/webhook"
    st.sidebar.info("💻 Ejecutándose localmente")

lumusai_url = st.sidebar.text_input("LumusAI URL", value=default_lumusai)
webhook_url = st.sidebar.text_input("Webhook URL", value=default_webhook)

# Verificar conexión
def check_lumusai_connection():
    try:
        response = requests.get(f"{lumusai_url}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

# Crear servidor de webhook
webhook_app = FastAPI()

@webhook_app.post("/webhook")
async def receive_webhook(request: Request):
    """Recibe webhooks de LumusAI."""
    try:
        payload = await request.json()
        payload["received_at"] = datetime.now().isoformat()
        
        # Agregar a session state
        st.session_state.received_webhooks.append(payload)
        
        # Actualizar estado de tarea activa
        task_id = payload.get('task_id')
        if task_id in st.session_state.active_tasks:
            st.session_state.active_tasks[task_id].update({
                'status': payload.get('status'),
                'completed_at': payload["received_at"],
                'processing_time': payload.get('processing_time'),
                'result': payload.get('data') if payload.get('status') == 'completed' else None,
                'error': payload.get('data', {}).get('error') if payload.get('status') == 'failed' else None
            })
        
        return {"status": "received", "task_id": task_id}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def start_webhook_server():
    """Inicia el servidor de webhook."""
    if not st.session_state.webhook_server_running:
        try:
            uvicorn.run(webhook_app, host="127.0.0.1", port=WEBHOOK_PORT, log_level="error")
        except:
            pass

# Estado de conexión
col1, col2 = st.columns(2)

with col1:
    st.subheader("🔗 Estado de Conexión")
    if check_lumusai_connection():
        st.success("✅ LumusAI está conectado y funcionando")
    else:
        st.error("❌ No se puede conectar a LumusAI")
        st.info("Asegúrate de que el contenedor esté corriendo: `docker ps`")

with col2:
    st.subheader("📡 Servidor de Webhook")
    if st.button("🚀 Iniciar Servidor de Webhook"):
        if not st.session_state.webhook_server_running:
            webhook_thread = threading.Thread(target=start_webhook_server, daemon=True)
            webhook_thread.start()
            st.session_state.webhook_server_running = True
            time.sleep(1)
            st.success(f"✅ Servidor iniciado en {webhook_url}")
        else:
            st.info("ℹ️ Servidor ya está corriendo")

# Sección principal - Procesamiento de documentos
st.header("📄 Procesamiento de Documentos")

# Tabs para diferentes tipos de procesamiento
tab1, tab2, tab3 = st.tabs(["🆕 Background Processing", "🔄 Synchronous Processing", "📊 Comparación"])

with tab1:
    st.subheader("🚀 Procesamiento en Background (Sin Timeouts)")
    
    col1, col2 = st.columns(2)
    
    with col1:
        action = st.selectbox("Tipo de Documento", ["cv", "invoice", "tutela_contestacion", "tutela_fallo"])
        
        input_type = st.radio("Tipo de Input", ["Texto", "Archivo"])
        
        if input_type == "Texto":
            if action == "cv":
                default_text = """Juan Pérez
Ingeniero de Software Senior
Email: <EMAIL>
Teléfono: +57 ************

EXPERIENCIA PROFESIONAL:
• Desarrollador Python Senior en TechCorp (2020-2024)
  - Desarrollo de APIs con FastAPI
  - Implementación de microservicios
  - Liderazgo de equipo de 5 desarrolladores

• Desarrollador Full Stack en StartupXYZ (2018-2020)
  - Aplicaciones web con React y Python
  - Bases de datos PostgreSQL
  - Integración de APIs REST

EDUCACIÓN:
• Ingeniería de Sistemas - Universidad Nacional (2014-2018)

HABILIDADES:
• Python (5 años), JavaScript (4 años)
• FastAPI, Django, React
• Docker, Kubernetes, AWS
• Machine Learning, NLP"""
            else:
                default_text = "Ingresa aquí el contenido del documento a procesar..."
            
            document_text = st.text_area("Contenido del Documento", value=default_text, height=200)
            file_input = None
        else:
            document_text = None
            file_input = st.file_uploader("Subir Archivo", type=['pdf', 'docx', 'txt'])
    
    with col2:
        st.info("🎯 **Ventajas del Background Processing:**\n\n"
                "✅ Respuesta inmediata (< 2 segundos)\n\n"
                "✅ Sin timeouts\n\n"
                "✅ Notificación por webhook\n\n"
                "✅ Seguimiento de progreso")
        
        use_webhook = st.checkbox("📡 Usar Webhook", value=True)
        if use_webhook:
            webhook_url_input = st.text_input("URL del Webhook", value=webhook_url)
        else:
            webhook_url_input = None
    
    if st.button("🚀 Procesar en Background", type="primary"):
        if not st.session_state.webhook_server_running and use_webhook:
            st.error("❌ Inicia el servidor de webhook primero")
        elif (document_text and document_text.strip()) or file_input:
            with st.spinner("Enviando tarea..."):
                try:
                    start_time = time.time()
                    
                    # Preparar datos
                    data = {"action": action}
                    if use_webhook:
                        data["webhook_url"] = webhook_url_input
                    
                    files = {}
                    if input_type == "Texto":
                        data["data"] = document_text
                    else:
                        files["file"] = file_input
                    
                    # Enviar request
                    response = requests.post(
                        f"{lumusai_url}/process-test",
                        data=data,
                        files=files,
                        timeout=30
                    )
                    
                    submission_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        result = response.json()
                        task_id = result.get("task_id")
                        
                        # Guardar tarea activa
                        st.session_state.active_tasks[task_id] = {
                            'task_id': task_id,
                            'action': action,
                            'status': 'processing',
                            'submitted_at': datetime.now().isoformat(),
                            'submission_time': submission_time,
                            'webhook_url': webhook_url_input if use_webhook else None
                        }
                        
                        st.success(f"✅ Tarea enviada exitosamente en {submission_time:.2f} segundos")
                        st.json(result)
                        
                    else:
                        st.error(f"❌ Error: {response.status_code}")
                        st.text(response.text)
                        
                except Exception as e:
                    st.error(f"❌ Error enviando tarea: {str(e)}")
        else:
            st.error("❌ Proporciona contenido del documento")

with tab2:
    st.subheader("🔄 Procesamiento Síncrono (Original)")
    st.warning("⚠️ **Advertencia:** Este procesamiento puede tomar 5-6 minutos y podría dar timeout")
    
    col1, col2 = st.columns(2)
    
    with col1:
        sync_action = st.selectbox("Tipo de Documento", ["cv", "invoice"], key="sync_action")
        sync_text = st.text_area("Contenido del Documento", value="Juan Pérez, Desarrollador Python, 5 años experiencia", key="sync_text")
    
    with col2:
        st.error("⚠️ **Problemas del Procesamiento Síncrono:**\n\n"
                "❌ Espera de 5-6 minutos\n\n"
                "❌ Riesgo de timeout\n\n"
                "❌ Bloquea la interfaz\n\n"
                "❌ No escalable")
    
    if st.button("🔄 Procesar Síncronamente", type="secondary"):
        if sync_text.strip():
            with st.spinner("Procesando... esto puede tomar 5-6 minutos"):
                try:
                    start_time = time.time()
                    
                    response = requests.post(
                        f"{lumusai_url}/process",
                        data={"action": sync_action, "data": sync_text},
                        timeout=600  # 10 minutos timeout
                    )
                    
                    processing_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        st.success(f"✅ Procesamiento completado en {processing_time:.2f} segundos")
                        st.json(response.json())
                    else:
                        st.error(f"❌ Error: {response.status_code}")
                        
                except requests.exceptions.Timeout:
                    st.error("⏰ TIMEOUT: El procesamiento tomó más de 10 minutos")
                except Exception as e:
                    st.error(f"❌ Error: {str(e)}")

with tab3:
    st.subheader("📊 Comparación de Rendimiento")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("🚀 Background Processing", "< 2 segundos", "Respuesta inmediata")
        st.metric("📡 Webhook Notification", "Automática", "Cuando se completa")
        st.metric("🔄 Escalabilidad", "Alta", "Múltiples tareas simultáneas")
    
    with col2:
        st.metric("🔄 Synchronous Processing", "5-6 minutos", "Espera completa")
        st.metric("⏰ Timeout Risk", "Alto", "Puede fallar")
        st.metric("🚫 Blocking", "Sí", "Bloquea interfaz")

# Sección de monitoreo
st.header("📊 Monitoreo de Tareas")

# Tareas activas
if st.session_state.active_tasks:
    st.subheader("🔄 Tareas Activas")
    
    for task_id, task_info in st.session_state.active_tasks.items():
        with st.expander(f"📋 Task: {task_id}"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"**Action:** {task_info['action']}")
                st.write(f"**Status:** {task_info['status']}")
                st.write(f"**Submitted:** {task_info['submitted_at']}")
            
            with col2:
                st.write(f"**Submission Time:** {task_info['submission_time']:.2f}s")
                if task_info.get('processing_time'):
                    st.write(f"**Processing Time:** {task_info['processing_time']:.2f}s")
                if task_info.get('webhook_url'):
                    st.write(f"**Webhook:** ✅ Configurado")
            
            with col3:
                if st.button(f"🔍 Check Status", key=f"status_{task_id}"):
                    try:
                        response = requests.get(f"{lumusai_url}/task/status/{task_id}")
                        if response.status_code == 200:
                            st.json(response.json())
                        else:
                            st.error(f"Error: {response.status_code}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
                
                if task_info['status'] in ['completed', 'failed'] and st.button(f"📄 Get Result", key=f"result_{task_id}"):
                    try:
                        response = requests.get(f"{lumusai_url}/task/result/{task_id}")
                        if response.status_code == 200:
                            st.json(response.json())
                        else:
                            st.error(f"Error: {response.status_code}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

# Webhooks recibidos
if st.session_state.received_webhooks:
    st.subheader("📡 Webhooks Recibidos")
    
    for i, webhook in enumerate(reversed(st.session_state.received_webhooks)):
        with st.expander(f"🔔 Webhook {len(st.session_state.received_webhooks) - i}: {webhook.get('task_id')}"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"**Task ID:** {webhook.get('task_id')}")
                st.write(f"**Status:** {webhook.get('status')}")
                st.write(f"**Processing Time:** {webhook.get('processing_time')}s")
                st.write(f"**Received At:** {webhook.get('received_at')}")
            
            with col2:
                if webhook.get('status') == 'completed':
                    st.success("✅ Procesamiento Exitoso")
                    if webhook.get('data'):
                        st.write("**Resultado disponible**")
                elif webhook.get('status') == 'failed':
                    st.error("❌ Procesamiento Falló")
                    if webhook.get('data', {}).get('error'):
                        st.write(f"**Error:** {webhook['data']['error']}")
            
            st.json(webhook)

# Footer
st.markdown("---")
st.markdown("🚀 **LumusAI Background Processing Test Client** - Emula aplicaciones reales conectándose al microservicio")

# Auto-refresh para webhooks
if st.session_state.webhook_server_running:
    time.sleep(1)
    st.rerun()
