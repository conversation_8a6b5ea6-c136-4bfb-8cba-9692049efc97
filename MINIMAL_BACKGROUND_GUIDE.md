# 🚀 Minimal Background Processing for LumusAI

## ✅ **Problem Solved: No More Timeouts!**

Your existing `/process` endpoint now has an optional `background=true` parameter that:
- **Returns immediately** with a task ID (no 5-6 minute wait)
- **Processes in background** using your exact same processing logic
- **Sends webhook** when complete
- **Keeps existing behavior** when background=false

## 🔄 **Two Processing Modes**

### **Mode 1: Normal Processing (Unchanged)**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf"
```
- Client waits 5-6 minutes
- Gets result directly
- **Works exactly like before**

### **Mode 2: Background Processing (New)**
```bash
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "background=true" \
     -F "webhook_url=https://your-app.com/webhook"
```
- **Returns immediately** with task ID
- Processes in background
- Get notified via webhook

## 🚀 **Quick Setup**

### 1. **Install Dependencies**
```bash
pip install aiohttp==3.10.10 aiosignal==1.3.1
```

### 2. **Start Service**
```bash
uvicorn main:app --reload
```

### 3. **Test Background Processing**
```bash
# Submit for background processing
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "background=true" \
     -F "webhook_url=http://localhost:8000/webhook/test"
```

**Immediate Response:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "processing",
  "message": "Document processing started in background",
  "check_status": "/task/status/cv-1703123456-abc12345",
  "get_result": "/task/result/cv-1703123456-abc12345",
  "webhook_url": "http://localhost:8000/webhook/test",
  "estimated_completion": "2-5 minutes"
}
```

### 4. **Check Status (Optional)**
```bash
curl -X GET "http://localhost:8000/task/status/cv-1703123456-abc12345"
```

### 5. **Get Result When Ready**
```bash
curl -X GET "http://localhost:8000/task/result/cv-1703123456-abc12345"
```

## 📡 **Available Endpoints**

| Endpoint | Method | Description | Response Time |
|----------|--------|-------------|---------------|
| `/process` | POST | Process document (normal or background) | Immediate if background=true |
| `/task/status/{task_id}` | GET | Check background task status | Immediate |
| `/task/result/{task_id}` | GET | Get background task result | Immediate |
| `/tasks/list` | GET | List all background tasks | Immediate |
| `/webhook/test` | POST | Test webhook endpoint | Immediate |

## 💡 **Usage Patterns**

### **Pattern 1: Background with Webhook**
```bash
# Submit and forget - get notified via webhook
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf" \
     -F "background=true" \
     -F "webhook_url=https://your-app.com/webhook"
```

### **Pattern 2: Background with Polling**
```bash
# Submit
TASK_ID=$(curl -X POST "http://localhost:8000/process" \
          -F "action=cv" \
          -F "file=@resume.pdf" \
          -F "background=true" | jq -r '.task_id')

# Poll for completion
while true; do
  STATUS=$(curl -s "/task/status/$TASK_ID" | jq -r '.status')
  if [ "$STATUS" != "processing" ]; then break; fi
  sleep 10
done

# Get result
curl -X GET "/task/result/$TASK_ID"
```

### **Pattern 3: Normal Processing (Existing Clients)**
```bash
# Existing clients work unchanged
curl -X POST "http://localhost:8000/process" \
     -F "action=cv" \
     -F "file=@resume.pdf"
```

## 🔔 **Webhook Notifications**

When background processing completes:

**Success:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "personal_info": { "full_name": "John Doe" },
    "skills": [...],
    "work_experience": [...]
  },
  "processing_time": 245.6
}
```

**Failure:**
```json
{
  "task_id": "cv-1703123456-abc12345",
  "status": "failed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "error": "Error processing document: Invalid file format"
  },
  "processing_time": 12.3
}
```

## 🛠️ **Integration Examples**

### **JavaScript Client**
```javascript
// Background processing
async function processDocumentBackground(file, action, webhookUrl) {
  const formData = new FormData();
  formData.append('action', action);
  formData.append('file', file);
  formData.append('background', 'true');
  if (webhookUrl) formData.append('webhook_url', webhookUrl);
  
  const response = await fetch('/process', {
    method: 'POST',
    body: formData
  });
  
  const { task_id } = await response.json();
  console.log(`Task submitted: ${task_id}`);
  return task_id;
}

// Check status
async function checkTaskStatus(taskId) {
  const response = await fetch(`/task/status/${taskId}`);
  return await response.json();
}

// Get result
async function getTaskResult(taskId) {
  const response = await fetch(`/task/result/${taskId}`);
  return await response.json();
}
```

### **Python Client**
```python
import requests

# Background processing
def process_document_background(file_path, action, webhook_url=None):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'action': action,
            'background': 'true'
        }
        if webhook_url:
            data['webhook_url'] = webhook_url
            
        response = requests.post('/process', files=files, data=data)
        return response.json()['task_id']

# Check status
def check_task_status(task_id):
    response = requests.get(f'/task/status/{task_id}')
    return response.json()

# Get result
def get_task_result(task_id):
    response = requests.get(f'/task/result/{task_id}')
    return response.json()
```

## ✨ **Key Benefits**

- **✅ No Timeouts** - Background requests return immediately
- **✅ Minimal Changes** - Your processing logic is untouched
- **✅ Backward Compatible** - Existing clients work unchanged
- **✅ Optional Background** - Use only when needed
- **✅ Same Quality** - Uses your exact same processors
- **✅ Webhook Support** - Get notified when done
- **✅ Simple Monitoring** - Check status and results anytime

## 🔧 **What Changed (Minimal)**

1. **Added `background` parameter** to `/process` endpoint
2. **Added background processing wrapper** that uses your existing processors
3. **Added 3 simple endpoints** for task management
4. **Zero changes** to your processing logic

## 📊 **Monitoring**

```bash
# List all background tasks
curl -X GET "http://localhost:8000/tasks/list"

# Check specific task
curl -X GET "http://localhost:8000/task/status/cv-1703123456-abc12345"

# View logs
tail -f logs/app.log | grep -i "background\|task"
```

## 🎯 **Perfect Solution For**

- **Existing clients** - Keep using normal mode
- **New integrations** - Use background mode to avoid timeouts
- **Webhook integrations** - Get notified when processing completes
- **Mobile apps** - Don't block UI for 5-6 minutes
- **API integrations** - Return immediately, process async

---

**🎉 Your timeout problem is solved with minimal code changes!** Your existing processing logic remains completely unchanged - just wrapped with optional background execution.
