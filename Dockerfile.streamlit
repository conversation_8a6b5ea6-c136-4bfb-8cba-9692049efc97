FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \
    streamlit==1.28.1 \
    requests==2.32.3 \
    fastapi==0.115.2 \
    uvicorn==0.32.0

# Copy application files
COPY streamlit_test_app.py .

# Create directory for logs
RUN mkdir -p /app/logs

# Expose ports
EXPOSE 8501 8002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Command to run Streamlit
CMD ["streamlit", "run", "streamlit_test_app.py", "--server.address", "0.0.0.0", "--server.port", "8501", "--server.headless", "true"]
