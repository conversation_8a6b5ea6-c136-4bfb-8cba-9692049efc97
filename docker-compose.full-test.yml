version: '3.8'

services:
  # LumusAI main application
  lumusai-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lumusai-app
    ports:
      - "8000:8000"
    environment:
      - API_KEY=${API_KEY}
      - API_VERSION=${API_VERSION:-2024-02-15-preview}
      - AZURE_ENDPOINT=${AZURE_ENDPOINT}
      - MODEL=${MODEL:-gpt-4o}
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - lumusai-network

  # Streamlit test application
  streamlit-test:
    build:
      context: .
      dockerfile: Dockerfile.streamlit
    container_name: streamlit-test
    ports:
      - "8501:8501"  # Streamlit UI
      - "8002:8002"  # Webhook server
    environment:
      - LUMUSAI_URL=http://lumusai-app:8000
      - WEBHOOK_URL=http://streamlit-test:8002/webhook
    depends_on:
      lumusai-app:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped
    networks:
      - lumusai-network

  # Simple webhook test server (alternative)
  webhook-server:
    image: python:3.12-slim
    container_name: webhook-server
    ports:
      - "8003:8003"
    volumes:
      - ./webhook_server.py:/app/webhook_server.py
    working_dir: /app
    command: |
      sh -c "
        pip install fastapi uvicorn requests &&
        python webhook_server.py
      "
    networks:
      - lumusai-network
    profiles:
      - webhook-only

networks:
  lumusai-network:
    driver: bridge

volumes:
  logs:
    driver: local
